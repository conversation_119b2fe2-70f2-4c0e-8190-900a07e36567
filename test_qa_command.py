#!/usr/bin/env python3
"""
Simple test script for the /qa command implementation.

This script tests the QA command parsing and basic functionality without
requiring actual Jira or database connections.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.qa_command import parse_qa_command_args


def test_parse_qa_command_args():
    """Test the command argument parsing logic."""
    print("Testing QA command argument parsing...")
    
    # Test cases
    test_cases = [
        # (input, expected_description, expected_robot_serial)
        ("", "", None),
        ("Simple description", "Simple description", None),
        ("slayer1 Robot is not working properly", "Robot is not working properly", "slayer1"),
        ("reaper5 Issue with navigation", "Issue with navigation", "reaper5"),
        ("SLAYER10 Multiple word description here", "Multiple word description here", "SLAYER10"),
        ("not_a_robot This should be treated as description", "not_a_robot This should be treated as description", None),
        ("slayer", "slayer", None),  # Just robot name without description
        ("slayer1", "slayer1", None),  # Just robot name without description
    ]
    
    all_passed = True
    
    for i, (input_text, expected_desc, expected_robot) in enumerate(test_cases, 1):
        description, robot_serial = parse_qa_command_args(input_text)
        
        if description == expected_desc and robot_serial == expected_robot:
            print(f"✅ Test {i}: PASSED")
            print(f"   Input: '{input_text}'")
            print(f"   Description: '{description}'")
            print(f"   Robot: {robot_serial}")
        else:
            print(f"❌ Test {i}: FAILED")
            print(f"   Input: '{input_text}'")
            print(f"   Expected: desc='{expected_desc}', robot={expected_robot}")
            print(f"   Got: desc='{description}', robot={robot_serial}")
            all_passed = False
        print()
    
    return all_passed


def test_jira_client_initialization():
    """Test Jira client initialization (without actual connection)."""
    print("Testing Jira client initialization...")
    
    try:
        # Set mock environment variables
        os.environ['JIRA_BASE_URL'] = 'https://test.atlassian.net/'
        os.environ['JIRA_USERNAME'] = '<EMAIL>'
        os.environ['JIRA_TOKEN'] = 'test_token'
        
        from app.jira_client import JiraClient
        
        # This should not fail with proper environment variables
        client = JiraClient()
        print("✅ Jira client initialization: PASSED")
        print(f"   Base URL: {client.base_url}")
        print(f"   Username: {client.username}")
        print(f"   Token: {'*' * len(client.token) if client.token else 'None'}")
        return True
        
    except Exception as e:
        print(f"❌ Jira client initialization: FAILED")
        print(f"   Error: {e}")
        return False
    finally:
        # Clean up environment variables
        for key in ['JIRA_BASE_URL', 'JIRA_USERNAME', 'JIRA_TOKEN']:
            if key in os.environ:
                del os.environ[key]


def test_jira_client_missing_config():
    """Test Jira client with missing configuration."""
    print("\nTesting Jira client with missing configuration...")
    
    try:
        # Ensure environment variables are not set
        for key in ['JIRA_BASE_URL', 'JIRA_USERNAME', 'JIRA_TOKEN']:
            if key in os.environ:
                del os.environ[key]
        
        from app.jira_client import JiraClient
        
        # This should fail with missing configuration
        client = JiraClient()
        print("❌ Missing config test: FAILED (should have raised ValueError)")
        return False
        
    except ValueError as e:
        print("✅ Missing config test: PASSED")
        print(f"   Expected ValueError: {e}")
        return True
    except Exception as e:
        print(f"❌ Missing config test: FAILED (unexpected error)")
        print(f"   Error: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("QA Command Implementation Test Suite")
    print("=" * 60)
    print()
    
    tests = [
        test_parse_qa_command_args,
        test_jira_client_initialization,
        test_jira_client_missing_config,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ Test {test_func.__name__} crashed: {e}")
            print("-" * 40)
    
    print()
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
