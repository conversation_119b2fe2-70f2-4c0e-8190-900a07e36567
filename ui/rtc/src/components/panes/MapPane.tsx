import {
  <PERSON>,
  CardContent,
  CircularProgress,
  Icon<PERSON>utton,
  Popover,
  Too<PERSON>ip,
  Typography,
} from "@mui/material";
import { ChannelLabel } from "@main/communication/rtc/dataChannelMessage.types";
import { classes } from "@main/utils/theme";
import { DateTime } from "luxon";
import { derefPoint } from "common/utils/farm";
import { FOOT_PER_METER, METER_PER_FOOT } from "@main/utils/units";
import { guessSerial } from "@main/logic/utils/robots";
import {
  LiveRtcDeviceResponse,
  useDeviceLocations,
} from "common/hooks/useLiveRtcLocation";
import { Map } from "@main/components/map/Map";
import { NavigationControl } from "react-map-gl";
import { normalizeFieldDefinitions } from "@main/utils/fieldDefinition";
import { NumberInput } from "@main/components/form/NumberInput";
import { Pane } from "@main/components/Pane";
import {
  selectDataChannelStates,
  selectDraftPlan,
  selectFocusedFieldId,
  selectLocation,
  selectNetworkStats,
  selectReportedAutonomyState,
} from "@main/store/driving.slice";
import {
  selectTractorWidthM,
  setTractorWidthM,
} from "@main/store/preferences.slice";
import { skipToken } from "@reduxjs/toolkit/query";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import { useCenterPivotLayers } from "common/components/map/layers/CenterPivotLayers";
import { useFarmLayers } from "common/components/map/layers/FarmLayers";
import { useFarmMapData } from "common/hooks/useFarmMapData";
import {
  useGetActiveObjectiveQuery,
  useGetJobQuery,
  useListTasksQuery,
} from "common/state/rtcJobsApi";
import { useGetFarmQuery } from "common/state/portalApi/farm";
import { useJobExplorer } from "@main/store/store";
import { useListFieldDefinitionsQuery } from "common/state/portalApi/fieldDefinition";
import { useObjectiveLayers } from "common/components/map/layers/ObjectiveLayers";
import { useTaskLayers } from "common/components/map/layers/TaskLayers";
import { useVideoStreamContext } from "@main/pages/stream/videoStreamContext";
import { viteImageResolver } from "common/utils/map/imageResolver.vite";
import GpsFixedIcon from "@mui/icons-material/GpsFixed";
import HeightIcon from "@mui/icons-material/Height";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import PivotIcon from "common/images/icons/irrigation.svg?react";
import React, { FC, useMemo, useState } from "react";
import StraightenIcon from "@mui/icons-material/Straighten";

interface MapPaneProps {}
export const MapPane: FC<MapPaneProps> = () => {
  const dispatch = useAppDispatch();

  const robotLocation = useAppSelector(selectLocation);
  const tractorWidthM = useAppSelector(selectTractorWidthM);
  const requestedAutonomyPath = useAppSelector(selectDraftPlan)?.path;
  const reportedAutonomyState = useAppSelector(selectReportedAutonomyState);

  const networkStats = useAppSelector(selectNetworkStats);
  const isDataRTCConnectionConnected = networkStats.rtcStatus === "connected";

  const { targetHostId } = useVideoStreamContext();
  const serial = guessSerial(targetHostId);
  const isGpsDataChannelOpen = useAppSelector(
    (state) => selectDataChannelStates(state)[ChannelLabel.GPS] === "open"
  );
  const isConnected = isGpsDataChannelOpen && isDataRTCConnectionConnected;

  const { data: fieldDefinitions } = useListFieldDefinitionsQuery({
    serial,
  });
  const focusedFieldId = useAppSelector(selectFocusedFieldId);

  const [selectedTractor, setSelectedTractor] = useState<string | undefined>(
    targetHostId
  );
  const [isFollowingTractor, setIsFollowingTractor] = useState<boolean>(true);
  const [showMapScaleBar, setShowMapScaleBar] = useState<boolean>(false);

  const isMapStale = robotLocation.current && !isConnected;

  const fieldBoundaries = useMemo(() => {
    if (!fieldDefinitions) {
      return [];
    }
    const boundaries = [];
    const normalizedDefs = normalizeFieldDefinitions(fieldDefinitions);

    for (const { fieldId, boundary, name } of normalizedDefs) {
      if (boundary) {
        boundaries.push({ fieldId, name, shape: boundary });
      }
    }
    return boundaries;
  }, [fieldDefinitions]);

  const { data: activeObjectiveResult } = useGetActiveObjectiveQuery(
    { serial },
    { refetchOnMountOrArgChange: 1, pollingInterval: 1000 }
  );
  const { data: job } = useGetJobQuery(
    activeObjectiveResult?.jobId
      ? { id: activeObjectiveResult.jobId.toString() }
      : skipToken
  );
  const { data: farm } = useGetFarmQuery(job?.farmId ? job.farmId : skipToken);

  const farmMapData = useFarmMapData(farm);
  const { Layers: FarmLayers, idsToLoad: farmIdsToLoad } = useFarmLayers({});
  const {
    assignmentsByTractorSerial,
    colorsBySerial,
    deviceIsMoving,
    deviceIsStale,
    liveDeviceData,
  } = useJobExplorer();
  // todo: re-evaluate if we move to specifying co-located zones
  const field = useMemo(() => {
    return job ? farm?.zones.find((z) => z.id?.id === job.fieldId) : undefined;
  }, [farm, job]);

  const centerPivot = useMemo(() => {
    // todo: change to it's own zone when the farm definition supports it
    return field?.contents?.field?.centerPivot?.center && farmMapData
      ? {
          ...field.contents.field.centerPivot,
          center: derefPoint(
            farmMapData.pointsById,
            field.contents.field.centerPivot.center
          ),
        }
      : undefined;
  }, [field, farmMapData]);
  const subscribeDevices = useMemo(
    () => (centerPivot?.endpointDeviceId ? [centerPivot.endpointDeviceId] : []),
    [centerPivot]
  );
  const { Layers: CenterPivotLayers, idsToLoad: centerPivotIdsToLoad } =
    useCenterPivotLayers({
      centerPivot,
    }) ?? { Layers: undefined, idsToLoad: [] };
  const liveCenterPivotData: LiveRtcDeviceResponse | undefined =
    centerPivot && liveDeviceData[centerPivot.endpointDeviceId]
      ? {
          location: liveDeviceData[centerPivot.endpointDeviceId],
          isMoving: Boolean(deviceIsMoving[centerPivot.endpointDeviceId]),
          isStale: Boolean(deviceIsStale[centerPivot.endpointDeviceId]),
          lastCheckedTimestamp: DateTime.now().toISO(),
        }
      : undefined;

  const { Layers: ObjectiveLayers, idsToLoad: objectiveIdsToLoad } =
    useObjectiveLayers({});

  const { Layers: TaskLayers, idsToLoad: taskIdsToLoad } = useTaskLayers({});

  useDeviceLocations(subscribeDevices);

  const { data: tasks } = useListTasksQuery(
    activeObjectiveResult
      ? {
          objectiveIds: [activeObjectiveResult?.id],
        }
      : skipToken
  );

  return (
    <Pane
      header={
        <MapPaneHeaderContent
          showLocateMe={isConnected}
          onToggleScaleBar={() => setShowMapScaleBar(!showMapScaleBar)}
          isScaleBarOn={showMapScaleBar}
          onLocateMeClick={() => {
            setSelectedTractor(targetHostId);
            setIsFollowingTractor(true);
          }}
          tractorWidthM={tractorWidthM}
          setTractorWidthM={(widthM: number) =>
            dispatch(setTractorWidthM(widthM))
          }
        />
      }
    >
      <div className="relative w-full h-[210px]">
        <Map
          isInactive={isMapStale || !robotLocation.current}
          selectedTractor={selectedTractor}
          setSelectedTractor={setSelectedTractor}
          isFollowingTractor={isFollowingTractor}
          setIsFollowingTractor={setIsFollowingTractor}
          tractors={
            robotLocation && targetHostId
              ? [
                  {
                    location: robotLocation,
                    name: targetHostId,
                    requestedAutonomyPath: requestedAutonomyPath || undefined,
                    currentAutonomyPath:
                      reportedAutonomyState.path || undefined,
                  },
                ]
              : []
          }
          fieldBoundaries={fieldBoundaries}
          focusedFieldId={focusedFieldId}
          showMapScaleBar={showMapScaleBar}
          tractorWidthM={tractorWidthM}
          imageConfig={{
            resolver: viteImageResolver,
            idsToLoad: [
              ...farmIdsToLoad,
              ...centerPivotIdsToLoad,
              ...objectiveIdsToLoad,
              ...taskIdsToLoad,
            ],
          }}
          extraControls={
            <>
              {CenterPivotLayers && centerPivot && liveCenterPivotData && (
                <CenterPivotLayers
                  liveData={liveCenterPivotData}
                  warnOnMove
                  centerPivot={centerPivot}
                  PivotIcon={PivotIcon}
                />
              )}
              {farmMapData && (
                <FarmLayers farmMapData={farmMapData} shownPoints={[]} />
              )}
              {job && (
                <>
                  <ObjectiveLayers
                    focusedObjective={undefined}
                    selectedObjective={undefined}
                    focusedTractor={undefined}
                    highlightedObjectives={[]}
                    hiddenTractors={[]}
                    objectives={job.objectives}
                    colorsBySerial={colorsBySerial}
                    assignmentsByTractorSerial={assignmentsByTractorSerial}
                  />
                  <TaskLayers tasks={tasks?.tasks ?? []} />
                </>
              )}
              <NavigationControl position="top-left" showZoom={false} />
            </>
          }
        />
        {!robotLocation.current && (
          <div className="absolute top-[calc(50%_-_30px)] left-[calc(50%_-_30px)] flex flex-col items-center gap-2">
            <div className="relative w-[40px] h-[40px]">
              <CircularProgress className="absolute top-0 left-0 animate-pulse" />
              <LocationOnIcon className="absolute top-[7px] left-[8px] fill-carbon-500 opacity-75" />
            </div>
            <div className="relative">
              <Typography
                className="text-white text-xs bg-rtc-gray-900 py-0.5 px-2 rounded-lg"
                variant="body1"
              >
                No GPS data
              </Typography>
            </div>
          </div>
        )}
      </div>
    </Pane>
  );
};

interface MapPaneHeaderContentProps {
  onToggleScaleBar: () => void;
  isScaleBarOn: boolean;
  onLocateMeClick: () => void;
  showLocateMe: boolean;
  tractorWidthM: number;
  setTractorWidthM: (widthM: number) => void;
}
const MapPaneHeaderContent: FC<MapPaneHeaderContentProps> = ({
  onToggleScaleBar,
  isScaleBarOn,
  onLocateMeClick,
  showLocateMe,
  tractorWidthM,
  setTractorWidthM,
}) => {
  return (
    <div className="flex justify-between">
      <div>Map</div>
      <div className="flex items-center gap-2">
        <WidthSelector widthM={tractorWidthM} setWidthM={setTractorWidthM} />
        <Tooltip title="Show reference scale bar">
          <IconButton
            size="small"
            className={classes({ "bg-blue-700 bg-opacity-85": isScaleBarOn })}
            onClick={onToggleScaleBar}
          >
            <StraightenIcon className="text-xs" />
          </IconButton>
        </Tooltip>
        {showLocateMe && (
          <Tooltip title="Center on tractor">
            <IconButton size="small" onClick={onLocateMeClick}>
              <GpsFixedIcon className="text-xs" />
            </IconButton>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

interface WidthSelectorProps {
  widthM: number;
  setWidthM: (widthM: number) => void;
}
const WidthSelector: FC<WidthSelectorProps> = ({ widthM, setWidthM }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | undefined>();
  const widthFt = widthM * FOOT_PER_METER;
  const round = (z: number): number => Math.round(z * 1e3) / 1e3;

  const [inputText, setInputText] = useState(String(round(widthFt)));

  const [lastWidthM, setLastWidthM] = useState(widthM);
  if (lastWidthM !== widthM) {
    setInputText(String(round(widthFt)));
    setLastWidthM(widthM);
    return;
  }

  const commit = (): void => {
    const inputNumber = Number.parseFloat(inputText);
    if (Number.isNaN(inputNumber)) {
      return;
    }
    setWidthM(inputNumber * METER_PER_FOOT);
  };

  return (
    <>
      <button
        className="flex items-center gap-1 bg-transparent border-0 border-b border-transparent text-white font-poppins font-bold hover:border-white p-0"
        onClick={(e) => setAnchorEl(e.currentTarget)}
      >
        <HeightIcon className="rotate-90 text-[15px]" />
        <span className="text-[10px] normal-case">
          {oneDecimal.format(widthFt)}
          &thinsp;ft
        </span>
      </button>
      <Popover
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(undefined)}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        transformOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Card>
          <CardContent>
            <NumberInput
              label="Tractor width (feet)"
              textFieldProps={{
                value: inputText,
                onChange: (e) => {
                  setInputText(e.target.value);
                },
                onKeyPress: (e) => {
                  if (e.key === "Enter") {
                    commit();
                  }
                },
                onBlur: commit,
              }}
            />
          </CardContent>
        </Card>
      </Popover>
    </>
  );
};

const oneDecimal = new Intl.NumberFormat(undefined, {
  maximumFractionDigits: 1,
});
