import { AnyObjectSchema, InferType } from "yup";
import { Expression } from "mapbox-gl";
import {
  getObjectiveFeature,
  ObjectiveFeature,
  TaskFeature,
} from "common/utils/rtcJobs";
import {
  InteractionLayer,
  LayerConfig,
  MapImageId,
} from "common/components/map/layers/types";
import {
  Objective,
  ObjectiveAssignment,
  Objective_ObjectiveType as ObjectiveType,
} from "protos/rtc/jobs";
import { renderGroundPrepSectionObjective } from "./objectiveRenderers/GroundPrepSection";
import { renderLaserWeedRowObjective } from "./objectiveRenderers/LaserWeedRow";
import React, { FC, useMemo } from "react";

export type RestrictedObjective<
  TObjectiveType extends ObjectiveType,
  TDataSchema extends AnyObjectSchema
> = Objective & { type: TObjectiveType; data: InferType<TDataSchema> };

interface ObjectiveRendererProps<
  TObjectiveType extends ObjectiveType,
  TDataSchema extends AnyObjectSchema
> {
  objectives: RestrictedObjective<TObjectiveType, TDataSchema>[];
  beforeId: string | undefined;
  api: ObjectiveRendererExpressionApi;
}

interface ObjectiveRenderer<
  TObjectiveType extends ObjectiveType,
  TDataSchema extends AnyObjectSchema
> {
  type: TObjectiveType;
  dataSchema: TDataSchema;
  Render: React.FC<ObjectiveRendererProps<TObjectiveType, TDataSchema>>;
  interactiveLayerIds: string[];
}

// this function looks like it does nothing and it does -- but calling it forces
// typescript to infer values for the type arguments (or throw a compile error
// if it can't) and gives you the type information on the return value for free.
export function makeObjectiveRenderer<
  TObjectiveType extends ObjectiveType,
  TDataSchema extends AnyObjectSchema
>(
  options: ObjectiveRenderer<TObjectiveType, TDataSchema>
): ObjectiveRenderer<TObjectiveType, TDataSchema> {
  return options;
}

export interface ObjectiveRendererExpressionApi {
  getObjective: Expression;
  getTractorAssignmentColor: Expression;
  getTractorAssignmentOpacity: Expression;
  isFocusedObjective: Expression;
  isSelectedObjective: Expression;
  isHighlightedObjective: Expression;
  isFirstHighlightedObjective: Expression;
  isFocusedTractorObjective: Expression;
}

interface ObjectiveLayersProps {
  objectives: Objective[];
  focusedObjective: number | undefined;
  selectedObjective: number | undefined;
  focusedTractor: string | undefined;
  highlightedObjectives: number[];
  hiddenTractors: string[];
  assignmentsByTractorSerial: Record<string, ObjectiveAssignment[]>;
  colorsBySerial: Record<string, string>;
  beforeId?: string;
}

const objectiveRenderers: ObjectiveRenderer<any, any>[] = [
  renderLaserWeedRowObjective,
  renderGroundPrepSectionObjective,
];

const mapboxGetObjective: Expression = ["get", "objective"];

export const ObjectiveLayers: FC<ObjectiveLayersProps> = ({
  objectives,
  focusedObjective,
  selectedObjective,
  highlightedObjectives,
  focusedTractor,
  hiddenTractors,
  assignmentsByTractorSerial,
  colorsBySerial,
  beforeId,
}) => {
  const colorsByObjectiveId: Record<string, string> = useMemo(() => {
    const colorsByObj: Record<string, string> = {};
    for (const [serial, assignments] of Object.entries(
      assignmentsByTractorSerial
    )) {
      const color = colorsBySerial[serial] ?? "#FF00DD";
      for (const assignment of assignments) {
        colorsByObj[assignment.objectiveId] = color;
      }
    }
    return colorsByObj;
  }, [assignmentsByTractorSerial, colorsBySerial]);

  const noColorObjectiveIds: (number | undefined)[] = useMemo(
    () =>
      hiddenTractors
        .flatMap((t) => assignmentsByTractorSerial[t])
        .flatMap((a) => a?.objectiveId ?? []),
    [hiddenTractors, assignmentsByTractorSerial]
  );

  const focusedTractorObjectiveIds: number[] = useMemo(() => {
    if (!focusedTractor) {
      return [];
    }
    const assignments = assignmentsByTractorSerial[focusedTractor];
    if (!assignments) {
      return [];
    }

    return assignments.map((a) => a.objectiveId);
  }, [focusedTractor, assignmentsByTractorSerial]);

  const mapboxGetColor: Expression = [
    "get",
    ["to-string", ["get", "id", mapboxGetObjective]],
    ["literal", colorsByObjectiveId],
  ];
  const mapboxGetOpacity: Expression = [
    "case",
    [
      "to-boolean",
      [
        "in",
        ["get", "id", mapboxGetObjective],
        ["literal", noColorObjectiveIds],
      ],
    ],
    0.01,
    ["to-boolean", mapboxGetColor],
    0.4,
    0.01,
  ];

  const highlightedObjectiveFilter: Expression = [
    "in",
    ["get", "id", mapboxGetObjective],
    ["literal", highlightedObjectives],
  ];

  const firstHighlightedObjectiveFilter: Expression =
    highlightedObjectives.length > 0
      ? [
          "==",
          ["get", "id", mapboxGetObjective],
          ["at", 0, ["literal", highlightedObjectives]],
        ]
      : ["literal", false];

  const mapBoxTractorFocusFilter: Expression = [
    "in",
    ["get", "id", mapboxGetObjective],
    ["literal", focusedTractorObjectiveIds],
  ];

  const api: ObjectiveRendererExpressionApi = {
    getObjective: mapboxGetObjective,
    getTractorAssignmentColor: mapboxGetColor,
    getTractorAssignmentOpacity: mapboxGetOpacity,
    isFocusedObjective: [
      "==",
      ["get", "id", mapboxGetObjective],
      // mapbox wants nulls to compare against, not undefineds
      // eslint-disable-next-line unicorn/no-null
      focusedObjective ?? null,
    ],
    isSelectedObjective: [
      "==",
      ["get", "id", mapboxGetObjective],
      // eslint-disable-next-line unicorn/no-null
      selectedObjective ?? null,
    ],
    isHighlightedObjective: highlightedObjectiveFilter,
    isFirstHighlightedObjective: firstHighlightedObjectiveFilter,
    isFocusedTractorObjective: mapBoxTractorFocusFilter,
  };

  // a note for future us: this block does some unsound things because
  // typescript's type system isn't expressive enough to allow us to "carry
  // along" the type information for each renderer and its objectives with it
  // (at least, not without some weird hacks.)  make sure that each element of
  // result has objectives with the correct type and validates against the
  // appropriate data schema before returning.
  const objectivesByType = useMemo(() => {
    const result: {
      renderer: ObjectiveRenderer<any, any>;
      objectives: Objective[];
    }[] = [];

    for (const renderer of objectiveRenderers) {
      const objectivesThisType = objectives.filter(
        (o) => o.type === renderer.type
      );

      const validObjectives = objectivesThisType.filter((o) =>
        renderer.dataSchema.validateSync(o.data)
      );

      if (objectivesThisType.length !== validObjectives.length) {
        console.warn(
          `ObjectiveLayers: ignoring some objectives with type ${renderer.type} and invalid data`
        );
      }

      result.push({
        renderer,
        objectives: validObjectives,
      });
    }

    return result;
  }, [objectives]);

  return (
    <>
      {objectivesByType.map(({ renderer, objectives }) => (
        <React.Fragment key={`objective-type-${renderer.type}`}>
          <renderer.Render
            objectives={objectives}
            beforeId={beforeId}
            api={api}
          />
        </React.Fragment>
      ))}
    </>
  );
};

export const useObjectiveLayers = ({
  onClick,
  onEnter,
  onLeave,
}: {
  onClick?: (feature?: ObjectiveFeature | TaskFeature) => void;
  onEnter?: (feature?: ObjectiveFeature | TaskFeature) => void;
  onLeave?: () => void;
}): LayerConfig<ObjectiveLayersProps> => {
  const interactionLayers = objectiveRenderers
    .flatMap((renderer) => renderer.interactiveLayerIds)
    .map<InteractionLayer>((id) => ({
      cursor: "pointer",
      id,
      onMouseMove: (f) => onEnter?.(getObjectiveFeature(f)),
      onLeave: () => onLeave?.(),
      onClick: (f) => onClick?.(getObjectiveFeature(f)),
    }));
  const Layers = ObjectiveLayers;
  const imagesToLoad: MapImageId[] = [];

  return { interactionLayers, Layers, idsToLoad: imagesToLoad };
};
